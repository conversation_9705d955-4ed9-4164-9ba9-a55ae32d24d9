import { environment } from '@env/environment'

export const configurationListConfig: any = [
  {
    id: 'content',
    type: '_container',
    items: ['table-1'],
    classes: 'flex-column panel'
  },
  {
    id: 'searchAdvanced',
    type: '_container',
    items: ['formControl-1', 'formControl-2'],
    classes: 'd-flex flex-row'
  },
  {
    id: 'formControl-1',
    data: {
      key: 'key',
      type: 'single',
      focus: false,
      label: '<PERSON>ã cấu hình',
      order: 1,
      reset: false,
      title: false,
      value: '',
      layout: '100',
      minRow: '2',
      checked: false,
      options: [],
      pattern: '',
      readOnly: false,
      required: false,
      template: '',
      updateOn: '',
      clearable: false,
      maxLength: 200,
      minLength: null,
      paramData: {
        key: '',
        url: '',
        isTag: false,
        value: '',
        preLoad: true,
        typeheadKey: '',
        defaultKeySearch: ''
      },
      upperCase: false,
      directives: '',
      checkBoxKey: 'value',
      controlType: 'textbox',
      placeholder: 'Nhập mã cấu hình',
      countMaxLength: true,
      customValidate: ['customMessageError'],
      tooltipMessage: '',
      requiredMessage: '',
      customDirectives: '',
      placeHolderSearch: ''
    },
    type: 'formControl',
    classes: 'w-50'
  },
  {
    id: 'formControl-2',
    data: {
      key: 'status',
      type: 'single',
      focus: false,
      label: 'Trạng thái',
      order: 2,
      reset: false,
      title: false,
      value: null,
      layout: '100',
      minRow: '2',
      checked: false,
      options: [
        {
          key: 'ACTIVE',
          value: 'Hoạt động'
        },
        {
          key: 'INACTIVE',
          value: 'Không hoạt động'
        }
      ],
      pattern: '',
      readOnly: false,
      required: false,
      template: '',
      updateOn: '',
      clearable: true,
      maxLength: 200,
      minLength: null,
      paramData: {},
      upperCase: false,
      directives: '',
      controlType: 'ngselect',
      placeholder: 'Chọn trạng thái',
      countMaxLength: false,
      customValidate: ['customMessageError'],
      tooltipMessage: '',
      requiredMessage: '',
      customDirectives: '',
      placeHolderSearch: ''
    },
    type: 'formControl',
    classes: 'w-50'
  },
  {
    id: 'table-1',
    data: {
      apiList: `${environment.services.portal}/v1/configurations`,
      pageSize: 10,
      apiCreate: '',
      apiDelete: `${environment.services.portal}/v1/configurations`,
      isTreeData: false,
      tableTitle: 'Cấu hình hệ thống',
      buttonLists: [
        {
          icon: 'add',
          name: 'Thêm mới',
          show: true,
          type: 'buttonCreate'
        }
      ],
      apiListMethod: 'GET',
      quickSearchFields: [
        {
          key: 'key',
          text: 'Mã cấu hình'
        }
      ],
      displayedColumns: [
        {
          name: 'Mã cấu hình',
          path: 'key',
          show: true,
          field: 'key',
          sort: false,
          filter: true
        },
        {
          name: 'Giá trị',
          path: 'value',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'value'
        },
        {
          name: 'Mô tả',
          path: 'description',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'description'
        },
        {},
        {
          name: 'Trạng thái',
          path: 'status',
          show: true,
          type: 'enumText',
          field: 'status',
          sort: false,
          filter: true,
          enumText: [
            {
              key: 'ACTIVE',
              text: 'Hoạt động',
              class: 'app-status-inprocess'
            },
            {
              key: 'INACTIVE',
              text: 'Không hoạt động',
              class: 'app-status-reject'
            }
          ]
        },
        {
          name: 'Thao tác',
          path: '',
          show: true,
          field: 'action'
        }
      ],
      columnActionLists: [
        {
          icon: 'edit',
          type: 'buttonEdit',
          navigationType: 'emit',
          routerName: ':component',
          class: '',
          title: 'Chỉnh sửa'
        },
        {
          icon: 'trash',
          type: 'buttonDelete',
          class: 'text-danger',
          navigationType: 'emit',
          routerName: ':component',
          title: 'Xóa'
        }
      ]
    },
    type: 'table',
    classes: ''
  }
]
